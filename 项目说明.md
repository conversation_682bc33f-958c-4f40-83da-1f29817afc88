# Markdown编辑器 - 专业的Markdown编辑工具

## 📖 项目概述

这是一个基于 Next.js 的现代化 Markdown 编辑器，具有实时预览、GitHub 集成和多平台导出功能。项目采用最新的前端技术栈，提供了专业级的编辑体验和丰富的功能特性。

## ✨ 核心特性

### 🎯 编辑功能
- **三种编辑模式**：源码模式、预览模式、分屏模式
- **实时预览**：编辑器与预览区域实时同步
- **语法高亮**：基于 CodeMirror 6 的 Markdown 语法高亮
- **自动补全**：智能的 Markdown 语法自动补全
- **搜索替换**：全文搜索和替换功能
- **滚动同步**：编辑器与预览区域滚动联动

### 🔗 GitHub 集成
- **OAuth 认证**：安全的 GitHub 账户登录
- **仓库管理**：直接连接和管理 GitHub 仓库
- **文件同步**：实时同步本地编辑与远程仓库
- **版本控制**：内置 Git 功能模拟
- **提交历史**：查看和管理提交记录

### 🎨 用户界面
- **现代化设计**：基于 Radix UI 的组件系统
- **主题切换**：明暗主题无缝切换
- **响应式布局**：完美适配桌面和移动设备
- **可定制界面**：灵活的侧边栏和工具栏配置

### 📁 文件管理
- **文件树视图**：直观的文件系统导航
- **文件操作**：创建、删除、重命名文件
- **大纲视图**：自动生成文档目录结构
- **搜索面板**：快速定位文件和内容

## 🏗️ 技术架构

### 前端框架
- **Next.js 13.5.1** - React 全栈框架，使用 App Router
- **React 18.2.0** - 用户界面库
- **TypeScript 5.2.2** - 类型安全的开发体验

### UI 组件系统
- **Radix UI** - 无样式、可访问的 UI 组件库
- **Tailwind CSS 3.3.3** - 实用优先的 CSS 框架
- **Lucide React** - 现代图标库
- **next-themes** - 主题切换支持

### 编辑器引擎
- **CodeMirror 6** - 高性能代码编辑器
  - 基础编辑功能包
  - Markdown 语法支持
  - 暗色主题支持
  - 自动补全功能
  - 搜索功能

### Markdown 处理
- **unified** - 统一的文本处理框架
- **remark** - Markdown 解析和处理
- **rehype** - HTML 处理和转换
- **remark-gfm** - GitHub Flavored Markdown 支持

### 代码高亮
- **Highlight.js** - 语法高亮库
- **Shiki** - 基于 VS Code 主题的语法高亮

### 状态管理
- **自定义 Hooks** - 基于 React Hooks 的状态管理
- **LocalStorage** - 本地状态持久化
- **Dexie** - IndexedDB 数据库操作

### 认证与集成
- **NextAuth.js** - 身份验证解决方案
- **GitHub OAuth** - GitHub 集成认证
- **GitHub API** - 仓库和文件操作

## 🚀 性能优化

### 渲染优化
- **增量解析**：只重新渲染变化的内容段落
- **自适应防抖**：智能防抖机制减少无效渲染
- **虚拟滚动**：大文档的高效滚动处理
- **边界索引**：高效的文档段落管理

### 内存管理
- **懒加载**：按需加载组件和资源
- **缓存策略**：智能的内容缓存机制
- **垃圾回收**：及时清理无用的渲染资源

## 📦 项目结构

```
├── app/                    # Next.js App Router
│   ├── api/               # API 路由
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 主页面
├── components/            # React 组件
│   ├── auth/             # 认证组件
│   ├── editor/           # 编辑器组件
│   ├── git/              # Git 相关组件
│   ├── github/           # GitHub 集成组件
│   ├── layout/           # 布局组件
│   ├── providers/        # 上下文提供者
│   └── ui/               # UI 基础组件
├── lib/                   # 工具库
│   ├── adaptive-debounce.ts    # 自适应防抖
│   ├── boundary-index.ts       # 边界索引
│   ├── github.ts              # GitHub API
│   ├── incremental-parser.ts  # 增量解析
│   ├── semantic-scroll.ts     # 语义滚动
│   ├── simpleGit.ts          # Git 模拟
│   ├── storage.ts            # 存储管理
│   └── utils.ts              # 通用工具
├── docs/                  # 文档
├── types/                 # TypeScript 类型定义
└── hooks/                 # 自定义 Hooks
```

## 🛠️ 开发环境

### 系统要求
- Node.js 18.0 或更高版本
- npm 或 yarn 包管理器
- 现代浏览器（Chrome、Firefox、Safari、Edge）

### 安装依赖
```bash
npm install
# 或
yarn install
```

### 环境配置
创建 `.env.local` 文件并配置以下环境变量：
```env
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
```

### 启动开发服务器
```bash
npm run dev
# 或
yarn dev
```

访问 `http://localhost:3000` 查看应用。

## 📋 使用指南

### 基本操作
1. **创建文档**：点击文件面板的"新建文件"按钮
2. **编辑内容**：在编辑器中输入 Markdown 内容
3. **实时预览**：切换到预览模式或分屏模式查看渲染效果
4. **保存文件**：使用 Ctrl+S 或点击保存按钮

### GitHub 集成
1. **登录 GitHub**：点击右上角的登录按钮
2. **连接仓库**：在 Git 面板中选择要连接的仓库
3. **同步文件**：编辑的内容会自动同步到 GitHub
4. **提交更改**：在 Git 面板中提交和推送更改

### 高级功能
- **大纲导航**：使用左侧大纲面板快速跳转到文档章节
- **全文搜索**：使用搜索面板查找文档内容
- **主题切换**：点击主题按钮切换明暗模式
- **导出功能**：将文档导出为多种格式

## 🤝 贡献指南

我们欢迎社区贡献！请遵循以下步骤：

1. Fork 项目仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。详情请查看 [LICENSE](LICENSE) 文件。

## 🙏 致谢

感谢以下开源项目的支持：
- [Next.js](https://nextjs.org/) - React 框架
- [CodeMirror](https://codemirror.net/) - 代码编辑器
- [Radix UI](https://www.radix-ui.com/) - UI 组件库
- [Tailwind CSS](https://tailwindcss.com/) - CSS 框架
- [unified](https://unifiedjs.com/) - 文本处理框架

## 📞 联系我们

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件
- 加入讨论群

---

**Markdown编辑器** - 让写作更加专业和高效！
