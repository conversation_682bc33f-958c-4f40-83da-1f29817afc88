'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Code, Eye, EyeOff, Settings } from 'lucide-react';
import CodeMirrorEditor from './CodeMirrorEditor';
import MarkdownRenderer from './MarkdownRenderer';
import NativeDOMRenderer from './NativeDOMRenderer';
import WYSIWYGEditor from './WYSIWYGEditor';
import { LocalStorage, useAutoSave } from '@/lib/storage';
// 临时导入M2渲染器
import { NativeDOMRendererM2 } from './NativeDOMRenderer-M2';
// 临时导入简化版本
import { NativeDOMRendererM2Fallback } from './NativeDOMRenderer-M2-fallback';

interface EditorAreaProps {
  onSettingsToggle: () => void;
  content?: string;
  onContentChange?: (content: string, currentFile?: string) => void;
  currentFile?: string | null; // 添加当前文件参数
  onScrollPositionChange?: (editorPos?: number, previewPos?: number) => void; // 滚动位置变化回调
  initialScrollPosition?: { editor?: number; preview?: number }; // 初始滚动位置
}

export function EditorArea({
  onSettingsToggle,
  content: externalContent,
  onContentChange,
  currentFile,
  onScrollPositionChange,
  initialScrollPosition
}: EditorAreaProps) {
  const [editorMode, setEditorMode] = useState<'source' | 'preview' | 'split' | 'wysiwyg'>('source');
  const [mounted, setMounted] = useState(false);
  const wysiwygRef = useRef<HTMLDivElement>(null); // WYSIWYG编辑器的引用

  // 客户端挂载检测和模式恢复
  useEffect(() => {
    setMounted(true);

    // 从localStorage恢复编辑器模式
    const savedMode = localStorage.getItem('editor-mode');
    if (savedMode && ['source', 'preview', 'split', 'wysiwyg'].includes(savedMode)) {
      setEditorMode(savedMode as 'source' | 'preview' | 'split' | 'wysiwyg');
    }
  }, []);

  // 保存编辑器模式到localStorage
  useEffect(() => {
    if (mounted) {
      localStorage.setItem('editor-mode', editorMode);
    }
  }, [editorMode, mounted]);

  // 滚动位置引用
  const editorScrollRef = useRef<HTMLDivElement>(null);
  const previewScrollRef = useRef<HTMLDivElement>(null);

  const defaultContent = `# 欢迎使用 Markdown编辑器

## 快速开始

这是一个**强大的** Markdown 编辑器，具有实时预览功能。

### 功能特性

- 🚀 实时预览
- 📝 源码编辑
- 🎨 自定义样式
- 📱 响应式设计
- 🌙 深色/浅色主题

### 代码示例

\`\`\`javascript
function hello() {
  console.log("你好，世界！");
}
\`\`\`

### 列表示例

1. 第一项
2. 第二项
3. 第三项

- 要点 1
- 要点 2
- 要点 3

> 这是一个引用示例。

---

## 滚动测试内容

### 第一部分

这里是一些测试内容，用来验证滚动功能是否正常工作。当内容超出容器高度时，应该能够正常滚动查看所有内容。

### 第二部分

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.

### 第三部分

Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

### 第四部分

Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.

### 第五部分

Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.

### 第六部分

Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit, sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem.

### 第七部分

Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur? Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur.

### 第八部分

At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident.

### 第九部分

Similique sunt in culpa qui officia deserunt mollitia animi, id est laborum et dolorum fuga. Et harum quidem rerum facilis est et expedita distinctio.

### 第十部分

Nam libero tempore, cum soluta nobis est eligendi optio cumque nihil impedit quo minus id quod maxime placeat facere possimus, omnis voluptas assumenda est, omnis dolor repellendus.

---

*愉快写作！现在您可以测试滚动功能了！*`;

  // 初始化内容 - 优先使用外部传入的内容，否则从本地存储加载或使用默认内容
  const [content, setContent] = useState(() => {
    if (externalContent !== undefined) {
      return externalContent;
    }
    if (typeof window !== 'undefined') {
      const savedContent = LocalStorage.loadContent();
      if (savedContent) {
        return savedContent;
      }
    }
    return defaultContent;
  });

  // 当外部内容变化时更新本地内容
  useEffect(() => {
    if (externalContent !== undefined && externalContent !== content) {
      setContent(externalContent);
    }
  }, [externalContent, content]);

  // 内容变化处理
  const handleContentChange = (newContent: string) => {
    setContent(newContent);
    if (onContentChange) {
      console.log(`📝 EditorArea: 内容变化，当前文件: ${currentFile}`);
      onContentChange(newContent, currentFile || undefined);
    }
  };

  // 处理模式切换
  const handleModeChange = (newMode: string) => {
    // 如果从WYSIWYG模式切换出去，先强制同步内容
    if (editorMode === 'wysiwyg' && newMode !== 'wysiwyg') {
      const wysiwygEditor = document.querySelector('[data-wysiwyg-editor]');
      if (wysiwygEditor && (wysiwygEditor as any).__forceSyncContent) {
        console.log('🔄 从WYSIWYG模式切换，强制同步内容...');
        (wysiwygEditor as any).__forceSyncContent();

        // 给一个短暂的延迟确保同步完成
        setTimeout(() => {
          setEditorMode(newMode as any);
        }, 10);
        return;
      }
    }

    setEditorMode(newMode as any);
  };

  // 启用自动保存
  useAutoSave(content);

  return (
    <div className="flex-1 flex flex-col editor-container editor-area">
      {/* Editor Toolbar */}
      <div className="h-10 bg-secondary/30 border-b border-border flex items-center justify-between px-4">
        <Tabs value={editorMode} onValueChange={handleModeChange}>
          <TabsList className="h-7">
            <TabsTrigger value="source" data-value="source" className="text-xs flex items-center space-x-1">
              <Code className="w-3 h-3" />
              <span>源码</span>
            </TabsTrigger>
            <TabsTrigger value="preview" data-value="preview" className="text-xs flex items-center space-x-1">
              <Eye className="w-3 h-3" />
              <span>预览</span>
            </TabsTrigger>
            <TabsTrigger value="split" data-value="split" className="text-xs flex items-center space-x-1">
              <EyeOff className="w-3 h-3" />
              <span>分屏</span>
            </TabsTrigger>
            <TabsTrigger value="wysiwyg" data-value="wysiwyg" className="text-xs flex items-center space-x-1">
              <Eye className="w-3 h-3" />
              <span>实时</span>
            </TabsTrigger>

          </TabsList>
        </Tabs>

        <Button variant="ghost" size="sm" onClick={onSettingsToggle}>
          <Settings className="w-4 h-4" />
        </Button>
      </div>

      {/* Editor Content */}
      <div className="flex-1 flex min-h-0 w-full">
        {editorMode === 'source' && (
          <div className="flex-1 editor-content w-full">
            <CodeMirrorEditor
              value={content}
              onChange={handleContentChange}
              placeholder="开始编写您的 Markdown..."
              className="h-full w-full"
              initialScrollPosition={initialScrollPosition?.editor}
              onScrollPositionChange={(pos) => onScrollPositionChange?.(pos, undefined)}
            />
          </div>
        )}

        {editorMode === 'preview' && (
          <div className="flex-1 editor-content w-full">
            <NativeDOMRendererM2Fallback 
              content={content}
              onTOCGenerated={(toc) => {
                console.log('📚 TOC生成完成:', toc?.toc?.length || 0, '个标题');
              }}
              onScrollPositionChanged={(pos) => {
                console.log('📍 滚动位置变化:', pos);
              }}
            />
          </div>
        )}

        {editorMode === 'split' && (
          <>
            <div className="flex-1 border-r border-border editor-content w-1/2">
              <CodeMirrorEditor
                value={content}
                onChange={handleContentChange}
                placeholder="开始编写您的 Markdown..."
                className="h-full w-full"
                initialScrollPosition={initialScrollPosition?.editor}
                onScrollPositionChange={(pos) => onScrollPositionChange?.(pos, undefined)}
              />
            </div>
            <div className="flex-1 editor-content w-1/2">
              <NativeDOMRendererM2Fallback 
                content={content}
                onTOCGenerated={(toc) => {
                  console.log('📚 TOC生成完成:', toc?.toc?.length || 0, '个标题');
                }}
                onScrollPositionChanged={(pos) => {
                  console.log('📍 滚动位置变化:', pos);
                }}
              />
            </div>
          </>
        )}

        {editorMode === 'wysiwyg' && (
          <div className="flex-1 editor-content w-full">
            <WYSIWYGEditor
              content={content}
              onChange={handleContentChange}
              placeholder="开始编写您的 Markdown..."
              className="h-full w-full"
              onModeSwitch={(mode) => setEditorMode(mode as any)}
            />
          </div>
        )}


      </div>
    </div>
  );
}