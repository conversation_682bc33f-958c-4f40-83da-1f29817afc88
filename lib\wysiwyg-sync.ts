/**
 * WYSIWYG 双向同步管理器
 * 处理编辑内容与 Markdown 源码的实时双向同步
 */

import { ParagraphMapper, ParagraphMapping } from './position-mapper';

export interface SyncOperation {
  type: 'replace' | 'insert' | 'delete';
  paragraphIndex: number;
  newContent: string;
  timestamp: number;
  source: 'user' | 'system';
}

export interface SyncResult {
  success: boolean;
  newMarkdown: string;
  operations: SyncOperation[];
  errors: string[];
}

/**
 * 双向同步管理器
 */
export class WYSIWYGSyncManager {
  private positionMapper: ParagraphMapper;
  private operationQueue: SyncOperation[] = [];
  private isProcessing = false;
  private lastMarkdownContent = '';
  private mutationObserver: MutationObserver | null = null;
  private debounceTimer: NodeJS.Timeout | null = null;
  private readonly debounceDelay = 200; // 200ms 防抖

  constructor() {
    this.positionMapper = new ParagraphMapper();
  }

  /**
   * 初始化同步管理器
   */
  initialize(container: HTMLElement, markdownContent: string): void {
    console.log('🔄 初始化双向同步管理器...');
    
    this.lastMarkdownContent = markdownContent;
    
    // 建立初始位置映射
    this.positionMapper.buildMapping(markdownContent, container);
    
    // 设置 DOM 变化监听
    this.setupMutationObserver(container);
    
    console.log('✅ 双向同步管理器初始化完成');
  }

  /**
   * 设置 DOM 变化监听
   */
  private setupMutationObserver(container: HTMLElement): void {
    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
    }

    this.mutationObserver = new MutationObserver((mutations) => {
      this.handleDOMMutations(mutations);
    });

    this.mutationObserver.observe(container, {
      childList: true,
      subtree: true,
      characterData: true,
      characterDataOldValue: true
    });

    console.log('👀 DOM 变化监听已设置');
  }

  /**
   * 处理 DOM 变化
   */
  private handleDOMMutations(mutations: MutationRecord[]): void {
    if (this.isProcessing) {
      console.log('⏸️ 同步处理中，跳过 DOM 变化');
      return;
    }

    console.log(`📝 检测到 ${mutations.length} 个 DOM 变化`);

    // 防抖处理
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    this.debounceTimer = setTimeout(() => {
      this.processDOMMutations(mutations);
    }, this.debounceDelay);
  }

  /**
   * 处理 DOM 变化（防抖后）
   */
  private processDOMMutations(mutations: MutationRecord[]): void {
    console.log('🔄 开始处理 DOM 变化...');

    const operations: SyncOperation[] = [];

    mutations.forEach((mutation) => {
      const operation = this.createOperationFromMutation(mutation);
      if (operation) {
        operations.push(operation);
      }
    });

    if (operations.length > 0) {
      this.queueOperations(operations);
      this.processOperationQueue();
    }
  }

  /**
   * 从 DOM 变化创建同步操作
   */
  private createOperationFromMutation(mutation: MutationRecord): SyncOperation | null {
    try {
      if (mutation.type === 'characterData') {
        // 文本内容变化
        const textNode = mutation.target;
        const parentElement = textNode.parentElement;
        
        if (parentElement) {
          const mapping = this.positionMapper.findMappingByDOM(parentElement);
          if (mapping) {
            return {
              type: 'replace',
              paragraphIndex: mapping.index,
              newContent: parentElement.textContent || '',
              timestamp: Date.now(),
              source: 'user'
            };
          }
        }
      } else if (mutation.type === 'childList') {
        // 节点添加/删除
        if (mutation.addedNodes.length > 0) {
          // 处理新增节点
          for (const node of Array.from(mutation.addedNodes)) {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as HTMLElement;
              // 简化处理：将新增内容作为新段落
              return {
                type: 'insert',
                paragraphIndex: -1, // 特殊标记，表示新增
                newContent: element.textContent || '',
                timestamp: Date.now(),
                source: 'user'
              };
            }
          }
        }
        
        if (mutation.removedNodes.length > 0) {
          // 处理删除节点
          return {
            type: 'delete',
            paragraphIndex: -1, // 特殊标记，表示删除
            newContent: '',
            timestamp: Date.now(),
            source: 'user'
          };
        }
      }

      return null;
    } catch (error) {
      console.error('创建同步操作失败:', error);
      return null;
    }
  }

  /**
   * 将操作加入队列
   */
  private queueOperations(operations: SyncOperation[]): void {
    this.operationQueue.push(...operations);
    console.log(`📋 操作队列: ${this.operationQueue.length} 个待处理操作`);
  }

  /**
   * 处理操作队列
   */
  private async processOperationQueue(): Promise<void> {
    if (this.isProcessing || this.operationQueue.length === 0) {
      return;
    }

    this.isProcessing = true;
    console.log('⚙️ 开始处理操作队列...');

    try {
      const result = await this.syncToMarkdown();
      
      if (result.success) {
        this.lastMarkdownContent = result.newMarkdown;
        console.log(`✅ 同步成功: ${result.newMarkdown.length} 字符`);
      } else {
        console.error('❌ 同步失败:', result.errors);
      }

      // 清空已处理的操作
      this.operationQueue = [];
      
    } catch (error) {
      console.error('操作队列处理异常:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 同步到 Markdown
   */
  private async syncToMarkdown(): Promise<SyncResult> {
    console.log('📄 开始同步到 Markdown...');

    const errors: string[] = [];
    const operations: SyncOperation[] = [...this.operationQueue];

    try {
      // 简化的同步策略：重新构建整个 Markdown
      const newMarkdown = this.rebuildMarkdownFromDOM();
      
      return {
        success: true,
        newMarkdown,
        operations,
        errors
      };
    } catch (error) {
      const errorMsg = `同步到 Markdown 失败: ${error}`;
      console.error(errorMsg);
      errors.push(errorMsg);
      
      return {
        success: false,
        newMarkdown: this.lastMarkdownContent,
        operations,
        errors
      };
    }
  }

  /**
   * 从 DOM 重新构建 Markdown
   */
  private rebuildMarkdownFromDOM(): string {
    const mappings = this.positionMapper.getAllMappings();
    const markdownLines: string[] = [];

    mappings.forEach((mapping) => {
      const domContent = mapping.domElement.textContent || '';
      const markdownLine = this.convertDOMToMarkdown(mapping, domContent);
      markdownLines.push(markdownLine);
    });

    return markdownLines.join('\n');
  }

  /**
   * 将 DOM 内容转换为 Markdown
   */
  private convertDOMToMarkdown(mapping: ParagraphMapping, content: string): string {
    switch (mapping.type) {
      case 'heading':
        const level = mapping.level || 1;
        return '#'.repeat(level) + ' ' + content;
        
      case 'list':
        const indent = '  '.repeat((mapping.level || 1) - 1);
        return indent + '- ' + content;
        
      case 'blockquote':
        return '> ' + content;
        
      case 'code':
        return '```\n' + content + '\n```';
        
      case 'paragraph':
      default:
        return content;
    }
  }

  /**
   * 手动触发同步
   */
  async manualSync(container: HTMLElement): Promise<SyncResult> {
    console.log('🔄 手动触发同步...');
    
    // 重新建立位置映射
    this.positionMapper.buildMapping(this.lastMarkdownContent, container);
    
    // 创建一个替换操作来触发同步
    const operation: SyncOperation = {
      type: 'replace',
      paragraphIndex: 0,
      newContent: container.textContent || '',
      timestamp: Date.now(),
      source: 'user'
    };

    this.queueOperations([operation]);
    await this.processOperationQueue();

    return {
      success: true,
      newMarkdown: this.lastMarkdownContent,
      operations: [operation],
      errors: []
    };
  }

  /**
   * 更新 Markdown 内容（从外部）
   */
  updateMarkdownContent(newContent: string, container: HTMLElement): void {
    console.log('📝 更新 Markdown 内容...');
    
    this.lastMarkdownContent = newContent;
    
    // 重新建立位置映射
    this.positionMapper.buildMapping(newContent, container);
  }

  /**
   * 获取当前 Markdown 内容
   */
  getCurrentMarkdown(): string {
    return this.lastMarkdownContent;
  }

  /**
   * 获取同步统计信息
   */
  getStats(): {
    queueLength: number;
    isProcessing: boolean;
    mappingStats: any;
  } {
    return {
      queueLength: this.operationQueue.length,
      isProcessing: this.isProcessing,
      mappingStats: this.positionMapper.getStats()
    };
  }

  /**
   * 销毁同步管理器
   */
  destroy(): void {
    console.log('🗑️ 销毁双向同步管理器...');
    
    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
      this.mutationObserver = null;
    }

    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }

    this.operationQueue = [];
    this.positionMapper.clearMappings();
    this.isProcessing = false;
    
    console.log('✅ 双向同步管理器已销毁');
  }
}
