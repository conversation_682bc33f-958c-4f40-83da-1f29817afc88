# WYSIWYG实时模式 - 第一阶段验收指南

## 📋 验收概述

**验收阶段**：第一阶段 - MVP基础功能  
**验收时间**：开发完成后  
**验收人员**：C（非技术人员）  
**验收方式**：可视化操作验证  

## 🎯 本阶段验收目标

验证WYSIWYG实时编辑模式的基础功能是否正常工作，包括：
- ✅ 新增的"实时"编辑模式可以正常使用
- ✅ 四种编辑模式可以正常切换
- ✅ 基础文本编辑功能正常
- ✅ 编辑内容能正确同步到源码
- ✅ 浏览器兼容性检测和降级机制正常

## 🖥️ 验收环境准备

### 1. 启动应用
```bash
# 在项目根目录执行
npm run dev
```
应用将在 `http://localhost:3000` 启动

### 2. 准备测试浏览器
- **主要测试**：Chrome 浏览器（最佳支持）
- **兼容性测试**：Firefox、Safari、Edge
- **降级测试**：较老版本浏览器或移动端

## 📝 详细验收步骤

### 步骤1：检查四种编辑模式

#### 1.1 查看编辑模式按钮
**操作**：打开应用，查看编辑器顶部工具栏

**预期结果**：
- 应该看到4个模式按钮：`源码`、`预览`、`分屏`、`实时`
- 默认应该选中`源码`模式
- 按钮应该有清晰的图标和文字标识

**验收标准**：
- [ ] 能看到4个编辑模式按钮
- [ ] 按钮布局整齐，文字清晰
- [ ] 默认选中源码模式

#### 1.2 测试模式切换
**操作**：依次点击每个模式按钮

**预期结果**：
- 点击`源码`：显示纯文本编辑器
- 点击`预览`：显示渲染后的内容
- 点击`分屏`：左侧源码，右侧预览
- 点击`实时`：显示可直接编辑的渲染内容

**验收标准**：
- [ ] 每个模式都能正常切换
- [ ] 切换时没有明显的卡顿或错误
- [ ] 内容在模式切换时保持一致

### 步骤2：测试WYSIWYG实时模式

#### 2.1 进入实时模式
**操作**：点击`实时`按钮

**预期结果**：
- 编辑器显示渲染后的内容（如标题、段落、格式化文本）
- 内容是可编辑的（光标可以定位，可以输入文字）
- 如果浏览器不支持，应该显示友好的提示信息

**验收标准**：
- [ ] 实时模式正常显示
- [ ] 内容可以编辑
- [ ] 不支持时有清晰提示

#### 2.2 基础文本编辑
**操作**：在实时模式下进行以下编辑：
1. 点击任意段落，输入一些文字
2. 选中文字，尝试删除
3. 在段落中间插入新文字
4. 创建新的段落（按回车键）

**预期结果**：
- 所有编辑操作都应该正常响应
- 文字输入流畅，没有明显延迟
- 格式保持正确

**验收标准**：
- [ ] 文字输入正常
- [ ] 删除操作正常
- [ ] 插入操作正常
- [ ] 新段落创建正常

#### 2.3 格式化文本编辑
**操作**：编辑包含格式的内容：
1. 编辑标题文字
2. 编辑加粗文本
3. 编辑斜体文本
4. 编辑链接文字

**预期结果**：
- 格式化文本可以正常编辑
- 格式在编辑过程中保持不变
- 编辑后格式仍然正确

**验收标准**：
- [ ] 标题可以编辑
- [ ] 加粗文本可以编辑
- [ ] 斜体文本可以编辑
- [ ] 链接文字可以编辑

### 步骤3：测试双向同步

#### 3.1 实时模式到源码模式同步
**操作**：
1. 在`实时`模式下编辑一些内容
2. 切换到`源码`模式
3. 检查源码是否包含刚才的编辑

**预期结果**：
- 在实时模式下的编辑应该反映在源码中
- Markdown语法应该正确
- 内容应该完整

**验收标准**：
- [ ] 编辑内容正确同步到源码
- [ ] Markdown语法正确
- [ ] 没有内容丢失

#### 3.2 源码模式到实时模式同步
**操作**：
1. 在`源码`模式下编辑Markdown内容
2. 切换到`实时`模式
3. 检查实时模式是否显示正确的渲染结果

**预期结果**：
- 源码的修改应该在实时模式中正确显示
- 格式化应该正确渲染
- 新增内容应该可见

**验收标准**：
- [ ] 源码修改正确同步到实时模式
- [ ] 格式渲染正确
- [ ] 新增内容正确显示

### 步骤4：测试浏览器兼容性

#### 4.1 支持的浏览器测试
**操作**：在Chrome、Firefox等现代浏览器中测试

**预期结果**：
- WYSIWYG模式正常工作
- 所有功能都可用
- 性能流畅

**验收标准**：
- [ ] Chrome浏览器完全支持
- [ ] Firefox浏览器正常工作
- [ ] 其他现代浏览器基本支持

#### 4.2 不支持浏览器的降级测试
**操作**：在较老的浏览器或移动端测试

**预期结果**：
- 应该显示"WYSIWYG模式不支持"的提示
- 自动切换到分屏模式
- 提示信息友好易懂

**验收标准**：
- [ ] 不支持时有清晰提示
- [ ] 自动降级到分屏模式
- [ ] 降级过程平滑

### 步骤5：性能和稳定性测试

#### 5.1 基础性能测试
**操作**：
1. 创建一个包含多个段落的文档（约50行）
2. 在实时模式下进行编辑
3. 观察响应速度

**预期结果**：
- 编辑响应应该在300ms以内
- 没有明显的卡顿
- 界面保持流畅

**验收标准**：
- [ ] 编辑响应及时
- [ ] 没有明显卡顿
- [ ] 界面流畅

#### 5.2 稳定性测试
**操作**：
1. 连续进行多次模式切换
2. 进行大量的编辑操作
3. 观察是否有错误或异常

**预期结果**：
- 应用保持稳定
- 没有崩溃或错误
- 功能持续正常

**验收标准**：
- [ ] 应用保持稳定
- [ ] 没有错误提示
- [ ] 功能持续正常

## 🐛 常见问题排查

### 问题1：实时模式无法编辑
**可能原因**：浏览器兼容性问题
**解决方案**：切换到Chrome浏览器重试

### 问题2：编辑内容没有同步
**可能原因**：同步机制延迟
**解决方案**：等待2-3秒后检查，或手动切换模式

### 问题3：格式显示异常
**可能原因**：Markdown语法错误
**解决方案**：检查源码模式中的Markdown语法

### 问题4：性能卡顿
**可能原因**：文档过大或浏览器性能不足
**解决方案**：减少文档大小或使用性能更好的设备

## ✅ 验收结果记录

### 功能验收结果
- [ ] 四种编辑模式正常
- [ ] WYSIWYG实时编辑正常
- [ ] 双向同步正常
- [ ] 浏览器兼容性正常
- [ ] 性能满足要求

### 发现的问题
请记录验收过程中发现的任何问题：

1. **问题描述**：
   **重现步骤**：
   **预期结果**：
   **实际结果**：

2. **问题描述**：
   **重现步骤**：
   **预期结果**：
   **实际结果**：

### 验收结论
- [ ] **通过**：所有功能正常，可以进入下一阶段开发
- [ ] **有条件通过**：主要功能正常，存在小问题但不影响使用
- [ ] **不通过**：存在重大问题，需要修复后重新验收

### 验收人签字
**验收人**：C  
**验收时间**：_______  
**签字**：_______

---

**注意事项**：
1. 验收过程中如有任何疑问，请及时联系开发人员
2. 请按照步骤顺序进行验收，确保全面覆盖
3. 发现问题时请详细记录，便于后续修复
4. 验收完成后请及时反馈结果
