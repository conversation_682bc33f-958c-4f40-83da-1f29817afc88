````markdown
# 专家答复 —— 实时模式「光标/输入失效」重构指南  
（直接复制给 aug 2）

---

## 1️⃣ 采用 **方案 A**（原生事件监听 + `preventDefault`）的理由

| 维度 | 方案 A（原生） | 方案 B（React onXxx） |
|------|---------------|-----------------------|
| 事件时序 | 与浏览器规范保持一致，能拦截 **所有 beforeinput/input/composition\*** | React 合成事件会 **延迟冒泡**，且 `beforeinput` 在旧 React 版本存在兼容差 |
| 光标控制 | 手动保存/恢复 selection，无虚拟 DOM 干扰 | React 重新渲染后常导致光标丢失，需额外 `useLayoutEffect` |
| 中文输入法 | 可以完整拿到 `compositionstart/end`，配合 `beforeinput` 处理 | React 合成事件易把 `compositionstart` 合并，难做 granular 控制 |
| 与同步管理器 | 原生事件 → `applyUserEdit()` → `MutationObserver` **一次** 触发 | React onInput 已修改 DOM，再次 diff 触发 MutationObserver **双重调用** |
| 维护复杂度 | **中**：一次性摒弃 React 事件 | **高**：需和 React diff 卡位、防止循环 |

> **结论**：**采用方案 A**。虽然初期工作量大，但能彻底消除光标跳转 & 输入失效；  
> 后续协作 (OT/CRDT) 也需 `beforeinput` 精准拦截，原生事件是更可持续的底座。

---

## 2️⃣ 事件处理 & 时序细节

1. **中文输入全链路**  
   ```text
   compositionstart → beforeinput(type=insertCompositionText) → input → compositionupdate*
   …(多次)…
   compositionend   → beforeinput(type=insertFromCompositionText) → input
````

2. 处理顺序

   ```typescript
   el.addEventListener('compositionstart', () => imeActive = true);
   el.addEventListener('beforeinput', (e) => {
     if (imeActive && e.inputType === 'insertCompositionText') return; // 让 IME 处理临时字符
     saveSelection();
     applyUserEdit(e);          // enqueue delta
     e.preventDefault();        // 阻止默认 DOM 改动
   });
   el.addEventListener('compositionend', (e) => {
     imeActive = false;
     saveSelection();
     applyUserEdit(e);          // 最终字符
     restoreSelection();
   });
   el.addEventListener('input', () => { /* noop – 已阻止默认行为 */ });
   ```
3. **防抖**

   * `beforeinput` 内不要调用 debounce；写入 `OpQueue` 后由 **renderLoop** 合并。
   * renderLoop：`requestAnimationFrame + AdaptiveDebounce`（80–200 ms）。

---

## 3️⃣ DOM 更新策略

| 操作                 | 做什么                                                            |
| ------------------ | -------------------------------------------------------------- |
| **beforeinput** 阶段 | 只往 **OpQueue** 写 delta，不改 DOM                                  |
| **renderLoop**     | 读取 `OpQueue` → **增量 Block diff** → `updateDOM()`（virtual list） |
| React 组件 re-render | 仅由外层状态 (blocks\[]) 触发；内部不再用 `dangerouslySetInnerHTML`          |

> **禁用**内部 `setState` 触发的再次渲染；块组件 `ParagraphBlock` 必须用 `memo(forwardRef)` 保持稳定。

---

## 4️⃣ 光标保存 / 恢复

```ts
export function saveSelection(): Range | null {
  const sel = window.getSelection();
  return sel && sel.rangeCount ? sel.getRangeAt(0).cloneRange() : null;
}
export function restoreSelection(r: Range | null) {
  if (!r) return;
  const sel = window.getSelection()!;
  sel.removeAllRanges(); sel.addRange(r);
}
```

* **保存**：在 *任何* DOM 改动前（beforeinput / compositionend）
* **恢复**：在 `applyUserEdit` 后、DOM patch 结束后一次（`requestAnimationFrame` 尾帧）。

---

## 5️⃣ React 集成策略

| 层                           | 责任                               | 约束                                   |
| --------------------------- | -------------------------------- | ------------------------------------ |
| **LiveRenderMode (client)** | 初始化原生事件、renderLoop、VirtualList   | `useEffect(() => attachListeners())` |
| **VirtualParagraphList**    | 只消费 `blocks[]`；无输入逻辑             | React 受控渲染                           |
| **ParagraphBlock**          | `memo + forwardRef`；内部不 setState | `ref={measureElement}`               |

> React 只负责 **块级视图**；真实输入流程完全交给原生事件 + Worker。

---

## 6️⃣ 与 `WYSIWYGSyncManager`

* **MutationObserver** 仅作 backup：监听外部协作/AI 注入的 DOM 变化。
* 在 `applyUserEdit()` 后：

  ```ts
  syncManager.applyLocalOps(opQueue.flush());
  ```
* Observer 回调中要 `if (isLocalChange) return;`，避免循环。

---

## 7️⃣ 下一步行动（优先级顺）

1. **移除 React onInput/onBeforeInput** 属性，统一改为 `useEffect` 原生监听。
2. 实现 `saveSelection / restoreSelection` ，并在 `beforeinput/compositionend` 使用。
3. 渲染链：OpQueue → renderLoop → blocks state → VirtualList。
4. 中文输入验证：安装系统中文 IME，输入 “测试”，确保 composition 不卡顿且 caret 不跳。
5. 长文（1500 段）测试指标：输入→paint <150 ms；FPS≥55。

---

### 如在重构中出现 “selection null” / “IME 卡住” / “OpQueue 阻塞” 等，贴代码段与具体堆栈，我将补给最小可运行示例。
