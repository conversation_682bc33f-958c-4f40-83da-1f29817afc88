/**
 * WYSIWYG 位置映射算法
 * 实现 DOM 节点与 Markdown 源码位置的双向映射
 */

export interface ParagraphMapping {
  index: number;                    // 段落索引
  markdownStart: number;           // Markdown 中的起始位置
  markdownEnd: number;             // Markdown 中的结束位置
  domElement: HTMLElement;         // 对应的 DOM 元素
  type: 'paragraph' | 'heading' | 'list' | 'blockquote' | 'code';
  level?: number;                  // 标题级别或列表嵌套级别
  content: string;                 // 原始内容
}

export interface PositionMappingResult {
  mappings: ParagraphMapping[];
  totalParagraphs: number;
  errors: string[];
}

/**
 * 段落级位置映射器
 */
export class ParagraphMapper {
  private mappings: ParagraphMapping[] = [];
  private errors: string[] = [];

  /**
   * 构建 Markdown 内容与 DOM 元素的映射表
   */
  buildMapping(markdownContent: string, container: HTMLElement): PositionMappingResult {
    console.log('🗺️ 开始构建位置映射...');
    
    this.mappings = [];
    this.errors = [];

    try {
      // 1. 解析 Markdown 内容为段落
      const markdownParagraphs = this.parseMarkdownParagraphs(markdownContent);
      console.log(`📝 解析出 ${markdownParagraphs.length} 个 Markdown 段落`);

      // 2. 遍历 DOM 元素
      const domElements = this.extractDOMElements(container);
      console.log(`🏗️ 找到 ${domElements.length} 个 DOM 元素`);

      // 3. 建立映射关系
      this.createMappings(markdownParagraphs, domElements);

      console.log(`✅ 位置映射完成: ${this.mappings.length} 个映射`);

      return {
        mappings: this.mappings,
        totalParagraphs: markdownParagraphs.length,
        errors: this.errors
      };
    } catch (error) {
      const errorMsg = `位置映射构建失败: ${error}`;
      console.error(errorMsg);
      this.errors.push(errorMsg);
      
      return {
        mappings: [],
        totalParagraphs: 0,
        errors: this.errors
      };
    }
  }

  /**
   * 解析 Markdown 内容为段落
   */
  private parseMarkdownParagraphs(content: string): Array<{
    index: number;
    start: number;
    end: number;
    content: string;
    type: ParagraphMapping['type'];
    level?: number;
  }> {
    const paragraphs: Array<{
      index: number;
      start: number;
      end: number;
      content: string;
      type: ParagraphMapping['type'];
      level?: number;
    }> = [];

    const lines = content.split('\n');
    let currentPosition = 0;
    let paragraphIndex = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const lineStart = currentPosition;
      const lineEnd = currentPosition + line.length;

      // 跳过空行
      if (line.trim() === '') {
        currentPosition = lineEnd + 1; // +1 for \n
        continue;
      }

      // 识别段落类型
      const paragraphInfo = this.identifyParagraphType(line);
      
      paragraphs.push({
        index: paragraphIndex++,
        start: lineStart,
        end: lineEnd,
        content: line,
        type: paragraphInfo.type,
        level: paragraphInfo.level
      });

      currentPosition = lineEnd + 1; // +1 for \n
    }

    return paragraphs;
  }

  /**
   * 识别段落类型
   */
  private identifyParagraphType(line: string): { type: ParagraphMapping['type']; level?: number } {
    const trimmed = line.trim();

    // 标题
    const headingMatch = trimmed.match(/^(#{1,6})\s+/);
    if (headingMatch) {
      return { type: 'heading', level: headingMatch[1].length };
    }

    // 列表
    if (trimmed.match(/^[\*\-\+]\s+/) || trimmed.match(/^\d+\.\s+/)) {
      // 计算嵌套级别
      const leadingSpaces = line.length - line.trimStart().length;
      const level = Math.floor(leadingSpaces / 2) + 1;
      return { type: 'list', level };
    }

    // 引用块
    if (trimmed.startsWith('>')) {
      return { type: 'blockquote' };
    }

    // 代码块
    if (trimmed.startsWith('```') || trimmed.startsWith('    ')) {
      return { type: 'code' };
    }

    // 默认为段落
    return { type: 'paragraph' };
  }

  /**
   * 提取 DOM 元素
   */
  private extractDOMElements(container: HTMLElement): HTMLElement[] {
    const elements: HTMLElement[] = [];

    // 使用 TreeWalker 遍历 DOM
    const walker = document.createTreeWalker(
      container,
      NodeFilter.SHOW_ELEMENT,
      {
        acceptNode: (node) => {
          const element = node as HTMLElement;
          
          // 只接受块级元素
          if (this.isBlockElement(element)) {
            return NodeFilter.FILTER_ACCEPT;
          }
          
          return NodeFilter.FILTER_SKIP;
        }
      }
    );

    let currentNode = walker.nextNode();
    while (currentNode) {
      elements.push(currentNode as HTMLElement);
      currentNode = walker.nextNode();
    }

    return elements;
  }

  /**
   * 判断是否为块级元素
   */
  private isBlockElement(element: HTMLElement): boolean {
    const blockTags = ['P', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'UL', 'OL', 'LI', 'BLOCKQUOTE', 'PRE', 'DIV'];
    return blockTags.includes(element.tagName);
  }

  /**
   * 创建映射关系
   */
  private createMappings(
    markdownParagraphs: Array<{
      index: number;
      start: number;
      end: number;
      content: string;
      type: ParagraphMapping['type'];
      level?: number;
    }>,
    domElements: HTMLElement[]
  ): void {
    // 简化的映射策略：按顺序一一对应
    const minLength = Math.min(markdownParagraphs.length, domElements.length);

    for (let i = 0; i < minLength; i++) {
      const mdParagraph = markdownParagraphs[i];
      const domElement = domElements[i];

      // 验证类型匹配
      const domType = this.getDOMElementType(domElement);
      if (mdParagraph.type !== domType) {
        this.errors.push(`类型不匹配: Markdown(${mdParagraph.type}) vs DOM(${domType}) at index ${i}`);
      }

      this.mappings.push({
        index: mdParagraph.index,
        markdownStart: mdParagraph.start,
        markdownEnd: mdParagraph.end,
        domElement: domElement,
        type: mdParagraph.type,
        level: mdParagraph.level,
        content: mdParagraph.content
      });
    }

    // 处理不匹配的情况
    if (markdownParagraphs.length !== domElements.length) {
      this.errors.push(`段落数量不匹配: Markdown(${markdownParagraphs.length}) vs DOM(${domElements.length})`);
    }
  }

  /**
   * 获取 DOM 元素类型
   */
  private getDOMElementType(element: HTMLElement): ParagraphMapping['type'] {
    const tagName = element.tagName.toLowerCase();

    if (tagName.match(/^h[1-6]$/)) {
      return 'heading';
    }

    if (tagName === 'ul' || tagName === 'ol' || tagName === 'li') {
      return 'list';
    }

    if (tagName === 'blockquote') {
      return 'blockquote';
    }

    if (tagName === 'pre' || tagName === 'code') {
      return 'code';
    }

    return 'paragraph';
  }

  /**
   * 根据 DOM 元素查找对应的映射
   */
  findMappingByDOM(element: HTMLElement): ParagraphMapping | null {
    return this.mappings.find(mapping => mapping.domElement === element) || null;
  }

  /**
   * 根据 Markdown 位置查找对应的映射
   */
  findMappingByMarkdownPosition(position: number): ParagraphMapping | null {
    return this.mappings.find(mapping => 
      position >= mapping.markdownStart && position <= mapping.markdownEnd
    ) || null;
  }

  /**
   * 获取所有映射
   */
  getAllMappings(): ParagraphMapping[] {
    return [...this.mappings];
  }

  /**
   * 清除映射
   */
  clearMappings(): void {
    this.mappings = [];
    this.errors = [];
  }

  /**
   * 获取映射统计信息
   */
  getStats(): {
    totalMappings: number;
    byType: Record<ParagraphMapping['type'], number>;
    errors: number;
  } {
    const byType: Record<ParagraphMapping['type'], number> = {
      paragraph: 0,
      heading: 0,
      list: 0,
      blockquote: 0,
      code: 0
    };

    this.mappings.forEach(mapping => {
      byType[mapping.type]++;
    });

    return {
      totalMappings: this.mappings.length,
      byType,
      errors: this.errors.length
    };
  }

  /**
   * 生成调试报告
   */
  getDebugReport(): string {
    const stats = this.getStats();
    
    let report = '=== 位置映射调试报告 ===\n\n';
    report += `总映射数: ${stats.totalMappings}\n`;
    report += `错误数: ${stats.errors}\n\n`;
    
    report += '按类型统计:\n';
    Object.entries(stats.byType).forEach(([type, count]) => {
      report += `  ${type}: ${count}\n`;
    });
    
    if (this.errors.length > 0) {
      report += '\n错误详情:\n';
      this.errors.forEach((error, index) => {
        report += `  ${index + 1}. ${error}\n`;
      });
    }
    
    return report;
  }
}
