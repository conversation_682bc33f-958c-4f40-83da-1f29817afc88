/**
 * WYSIWYG 浏览器兼容性检测工具
 * 检测浏览器是否支持 WYSIWYG 编辑所需的核心功能
 */

export interface CompatibilityResult {
  isSupported: boolean;
  features: {
    contentEditable: boolean;
    mutationObserver: boolean;
    treeWalker: boolean;
    selection: boolean;
    range: boolean;
    execCommand: boolean;
  };
  browserInfo: {
    userAgent: string;
    isChrome: boolean;
    isFirefox: boolean;
    isSafari: boolean;
    isEdge: boolean;
    isMobile: boolean;
  };
  issues: string[];
  recommendations: string[];
}

/**
 * 检测 ContentEditable 支持
 */
function checkContentEditableSupport(): boolean {
  try {
    const div = document.createElement('div');
    div.contentEditable = 'true';
    
    // 检测是否可以设置 contentEditable
    if (div.contentEditable !== 'true') {
      return false;
    }
    
    // 检测是否支持基本的编辑事件
    const hasInputEvent = 'oninput' in div;
    const hasBeforeInputEvent = 'onbeforeinput' in div;
    
    return hasInputEvent; // beforeInput 不是必需的，但 input 是必需的
  } catch (error) {
    console.error('ContentEditable 检测失败:', error);
    return false;
  }
}

/**
 * 检测 MutationObserver 支持
 */
function checkMutationObserverSupport(): boolean {
  try {
    return typeof MutationObserver !== 'undefined' && 
           typeof MutationObserver.prototype.observe === 'function';
  } catch (error) {
    console.error('MutationObserver 检测失败:', error);
    return false;
  }
}

/**
 * 检测 TreeWalker 支持
 */
function checkTreeWalkerSupport(): boolean {
  try {
    return typeof TreeWalker !== 'undefined' && 
           typeof document.createTreeWalker === 'function';
  } catch (error) {
    console.error('TreeWalker 检测失败:', error);
    return false;
  }
}

/**
 * 检测 Selection API 支持
 */
function checkSelectionSupport(): boolean {
  try {
    return typeof window.getSelection === 'function' &&
           typeof Selection !== 'undefined';
  } catch (error) {
    console.error('Selection API 检测失败:', error);
    return false;
  }
}

/**
 * 检测 Range API 支持
 */
function checkRangeSupport(): boolean {
  try {
    return typeof Range !== 'undefined' &&
           typeof document.createRange === 'function';
  } catch (error) {
    console.error('Range API 检测失败:', error);
    return false;
  }
}

/**
 * 检测 execCommand 支持（用于某些编辑操作）
 */
function checkExecCommandSupport(): boolean {
  try {
    return typeof document.execCommand === 'function';
  } catch (error) {
    console.error('execCommand 检测失败:', error);
    return false;
  }
}

/**
 * 获取浏览器信息
 */
function getBrowserInfo() {
  const userAgent = navigator.userAgent;
  
  return {
    userAgent,
    isChrome: /Chrome/.test(userAgent) && !/Edge/.test(userAgent),
    isFirefox: /Firefox/.test(userAgent),
    isSafari: /Safari/.test(userAgent) && !/Chrome/.test(userAgent),
    isEdge: /Edge/.test(userAgent) || /Edg\//.test(userAgent),
    isMobile: /Mobile|Android|iPhone|iPad/.test(userAgent)
  };
}

/**
 * 生成兼容性问题和建议
 */
function generateIssuesAndRecommendations(
  features: CompatibilityResult['features'],
  browserInfo: CompatibilityResult['browserInfo']
): { issues: string[]; recommendations: string[] } {
  const issues: string[] = [];
  const recommendations: string[] = [];

  // 检查核心功能缺失
  if (!features.contentEditable) {
    issues.push('浏览器不支持 ContentEditable 功能');
    recommendations.push('请升级到支持 HTML5 的现代浏览器');
  }

  if (!features.mutationObserver) {
    issues.push('浏览器不支持 MutationObserver API');
    recommendations.push('请升级浏览器版本，建议使用 Chrome 26+、Firefox 14+、Safari 7+');
  }

  if (!features.treeWalker) {
    issues.push('浏览器不支持 TreeWalker API');
    recommendations.push('请升级浏览器版本，TreeWalker 在所有现代浏览器中都有支持');
  }

  if (!features.selection) {
    issues.push('浏览器不支持 Selection API');
    recommendations.push('请升级到支持 Selection API 的浏览器版本');
  }

  // 浏览器特定的问题
  if (browserInfo.isSafari) {
    issues.push('Safari 浏览器的 ContentEditable 行为可能不稳定');
    recommendations.push('建议使用 Chrome 或 Firefox 以获得最佳体验');
  }

  if (browserInfo.isMobile) {
    issues.push('移动端浏览器的 WYSIWYG 编辑体验可能受限');
    recommendations.push('移动端建议使用源码模式或分屏模式');
  }

  // 如果没有问题，提供优化建议
  if (issues.length === 0) {
    if (browserInfo.isChrome) {
      recommendations.push('Chrome 浏览器提供最佳的 WYSIWYG 编辑体验');
    } else if (browserInfo.isFirefox) {
      recommendations.push('Firefox 浏览器对 WYSIWYG 编辑有良好支持');
    }
  }

  return { issues, recommendations };
}

/**
 * 执行完整的兼容性检测
 */
export function checkWYSIWYGCompatibility(): CompatibilityResult {
  console.log('🔍 开始 WYSIWYG 兼容性检测...');

  const features = {
    contentEditable: checkContentEditableSupport(),
    mutationObserver: checkMutationObserverSupport(),
    treeWalker: checkTreeWalkerSupport(),
    selection: checkSelectionSupport(),
    range: checkRangeSupport(),
    execCommand: checkExecCommandSupport()
  };

  const browserInfo = getBrowserInfo();

  // 核心功能检测：ContentEditable、MutationObserver、TreeWalker、Selection 是必需的
  const isSupported = features.contentEditable && 
                     features.mutationObserver && 
                     features.treeWalker && 
                     features.selection;

  const { issues, recommendations } = generateIssuesAndRecommendations(features, browserInfo);

  const result: CompatibilityResult = {
    isSupported,
    features,
    browserInfo,
    issues,
    recommendations
  };

  console.log('✅ WYSIWYG 兼容性检测完成:', result);

  return result;
}

/**
 * 简化的兼容性检测（仅返回是否支持）
 */
export function isWYSIWYGSupported(): boolean {
  try {
    const result = checkWYSIWYGCompatibility();
    return result.isSupported;
  } catch (error) {
    console.error('兼容性检测异常:', error);
    return false;
  }
}

/**
 * 获取兼容性检测的详细报告（用于调试）
 */
export function getCompatibilityReport(): string {
  const result = checkWYSIWYGCompatibility();
  
  let report = '=== WYSIWYG 兼容性检测报告 ===\n\n';
  
  report += `总体支持状态: ${result.isSupported ? '✅ 支持' : '❌ 不支持'}\n\n`;
  
  report += '功能支持详情:\n';
  Object.entries(result.features).forEach(([feature, supported]) => {
    report += `  ${feature}: ${supported ? '✅' : '❌'}\n`;
  });
  
  report += `\n浏览器信息:\n`;
  report += `  User Agent: ${result.browserInfo.userAgent}\n`;
  report += `  Chrome: ${result.browserInfo.isChrome ? '✅' : '❌'}\n`;
  report += `  Firefox: ${result.browserInfo.isFirefox ? '✅' : '❌'}\n`;
  report += `  Safari: ${result.browserInfo.isSafari ? '✅' : '❌'}\n`;
  report += `  Edge: ${result.browserInfo.isEdge ? '✅' : '❌'}\n`;
  report += `  Mobile: ${result.browserInfo.isMobile ? '✅' : '❌'}\n`;
  
  if (result.issues.length > 0) {
    report += `\n发现的问题:\n`;
    result.issues.forEach((issue, index) => {
      report += `  ${index + 1}. ${issue}\n`;
    });
  }
  
  if (result.recommendations.length > 0) {
    report += `\n建议:\n`;
    result.recommendations.forEach((rec, index) => {
      report += `  ${index + 1}. ${rec}\n`;
    });
  }
  
  return report;
}
