````markdown
# 专家答复 —— “光标固定” 的根因 + 两阶段可行方案

> **核心结论**  
> 光标被锁定在首位，是因为：  
> 1. 你们在 `beforeinput` 里 **preventDefault**，阻断了浏览器自身的插入逻辑；  
> 2. 随后通过 `dangerouslySetInnerHTML` **整体替换 innerHTML**，React 重建了节点，原 Range 对象已失效。  
> 若继续「完全手动 DOM 操作」路线，需要实现**完整输入类型处理器**，成本极高。  
> **更简单、稳定的路径**：让浏览器执行默认 DOM 操作，再 “_被动_ ”地把 DOM 变更同步到 Block Model。  
> 下面给出 **Phase 1 (2 天) 可落地方案** + **Phase 2 (后续增强)**。

---

## Phase 1 —— 保留浏览器输入，**只做被动同步**（最快修复光标）

### 1. 组件骨架

```tsx
function EditorCore({ blocks, onBlocksChange }: Props) {
  const ref = useRef<HTMLDivElement>(null);

  /* 1⃣ 初始化：用 dangerouslySetInnerHTML 仅渲染一次 */
  useEffect(() => {
    ref.current!.innerHTML = blocksToHTML(blocks);   // 初次或 external load
  }, [/* fileId */]);                                // 切文件时重置

  /* 2⃣ 监听 beforeinput / input / composition */
  useEffect(() => {
    const el = ref.current!;
    const handleInput = throttle(() => {
      // 把最新 DOM 转换回 Block 数组
      const newBlocks = htmlToBlocks(el);
      onBlocksChange(newBlocks);            // 仅更新外部状态，不重设 innerHTML
    }, 80);
    el.addEventListener('input', handleInput);
    return () => el.removeEventListener('input', handleInput);
  }, []);

  return (
    <div
      ref={ref}
      contentEditable
      suppressContentEditableWarning
      className="prose max-w-none outline-none"
    />
  );
}
````

*要点*

* **不再 preventDefault**；浏览器负责插入/删除，Caret 正常移动。
* **React 只渲染一次**；后续内容变化通过 DOM→Blocks→VirtualList 生效。
* `htmlToBlocks` 可先用行级 split + `innerText` 生成粗略段落，后续再细化 Markdown 解析。
* `throttle(80 ms)` 即可配合 AdaptiveDebounce。

### 2. Blocks → VirtualList 一致性

1. 在 `onBlocksChange(newBlocks)` 里 **setBlocks(newBlocks)** → 触发 VirtualList。
2. 因 **contentEditable DOM 已是最新**，不需要再次 `dangerouslySetInnerHTML`。
3. 外部切文件：

   ```tsx
   <EditorCore key={fileId} blocks={file.blocks} onBlocksChange={…} />
   ```

   利用 React `key` 强制重新 mount，安全清理内部 DOM。

### 3. 优点 & 费控

|          | 方案 A（保留默认输入）                       |
| -------- | ---------------------------------- |
| 光标 / IME | 浏览器原生处理，无需手动 Range 修复              |
| 复杂格式     | 链接、加粗、粘贴富文本都能工作（先作为 HTML block 保存） |
| 开发成本     | 只实现 **DOM→Blocks diff**；后期再优化      |
| React 冲突 | 完全避免 “React 重新渲染 → 光标丢失”           |

*(你们当前方案 B 相当于自行复制 contenteditable 引擎 —— 建议放弃)*

---

## Phase 2 —— 进阶：自定义 `beforeinput` 仅处理格式化命令

等 Phase 1 跑稳后，再按下面顺序增强：

1. **格式化快捷键**（Ctrl+B、Cmd+K…）：

   * 在 `beforeinput` 里检查 `inputType === 'formatBold'`…
   * `preventDefault()` → 执行自家命令→同步 Blocks。
2. **粘贴清洗**： `paste` 事件里拦截外部 HTML→Markdown 转换。
3. **Worker 增量解析**：把 `htmlToBlocks` 移到 Worker；主线程仅传 innerHTML 片段。
4. **协作**：MutationObserver / OT / CRDT 门槛降低——只需监听 DOM patch。

---

## 回答提问

| # | 问题                        | 答复                                                                        |
| - | ------------------------- | ------------------------------------------------------------------------- |
| 1 | **完全禁用 React 重新渲染？**      | 是。编辑器内容由 contentEditable 自行更新；React 只管理外层容器 & VirtualList。                |
| 2 | **外部文件切换？**               | 通过 `key={fileId}` remount；或手动 `ref.current!.innerHTML = newHTML`。         |
| 3 | **需要实现完整 inputType 处理吗？** | Phase 1 不需要；只负责 DOM→Blocks。完整处理可在 Phase 2 针对个别格式化命令增量覆盖。                  |
| 4 | **状态同步但不渲染？**             | Blocks 数组仍存 React state；VirtualList 受控渲染。编辑器自身 DOM 由浏览器维护，无需 setState 回写。 |
| 5 | **最小可行实现？**               | 上述 EditorCore + htmlToBlocks(line-split) + throttle 80 ms 即可。             |

---

## 下一步行动

1. **实现 EditorCore 骨架**（Phase 1）：

   * 删除现有 `beforeinput preventDefault`；
   * 保存/恢复光标逻辑可先移除；浏览器 caret 已够稳定。
2. **测指标**：1500 段 → 输入 100 字符：P95 < 150 ms；FPS > 55；DOM ≤ 2500。
3. **如光标仍跳**：发 DevTools Recording + 关键源码段；我给具体 patch。

完成 Phase 1 后，你们即可恢复正常输入且光标可移动，再进入性能和格式增强。
加油！🚀

```
::contentReference[oaicite:0]{index=0}
```
