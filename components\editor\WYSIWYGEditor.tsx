'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkGfm from 'remark-gfm';
import remarkRehype from 'remark-rehype';
import rehypeHighlight from 'rehype-highlight';
import rehypeStringify from 'rehype-stringify';
import rehypeRaw from 'rehype-raw';
import DOMPurify from 'dompurify';
import { checkWYSIWYGCompatibility, getCompatibilityReport } from '@/lib/wysiwyg-support';
import { WYSIWYGSyncManager } from '@/lib/wysiwyg-sync';

// 光标位置保存和恢复工具函数
function saveSelection(): Range | null {
  const sel = window.getSelection();
  return sel && sel.rangeCount ? sel.getRangeAt(0).cloneRange() : null;
}

function restoreSelection(range: Range | null): void {
  if (!range) return;
  const sel = window.getSelection();
  if (sel) {
    sel.removeAllRanges();
    sel.addRange(range);
  }
}

interface WYSIWYGEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  className?: string;
  onModeSwitch?: (mode: string) => void;
}

export const WYSIWYGEditor: React.FC<WYSIWYGEditorProps> = ({
  content,
  onChange,
  placeholder = '开始编写您的 Markdown...',
  className = '',
  onModeSwitch
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isSupported, setIsSupported] = useState(true);
  const [debugInfo, setDebugInfo] = useState<string>('');
  const [isInitialized, setIsInitialized] = useState(false);
  // Phase 1: 移除不再需要的refs和state

  // Markdown 处理器
  const processor = unified()
    .use(remarkParse)
    .use(remarkGfm)
    .use(remarkRehype, { allowDangerousHtml: true })
    .use(rehypeHighlight)
    .use(rehypeRaw)
    .use(rehypeStringify);

  // 浏览器兼容性检测
  const checkWYSIWYGSupport = useCallback((): boolean => {
    try {
      const compatibilityResult = checkWYSIWYGCompatibility();

      // 设置详细的调试信息
      if (compatibilityResult.isSupported) {
        setDebugInfo(`✅ 兼容性检测通过 - ${compatibilityResult.browserInfo.isChrome ? 'Chrome' :
                      compatibilityResult.browserInfo.isFirefox ? 'Firefox' :
                      compatibilityResult.browserInfo.isSafari ? 'Safari' :
                      compatibilityResult.browserInfo.isEdge ? 'Edge' : '未知浏览器'}`);
      } else {
        const issues = compatibilityResult.issues.join(', ');
        setDebugInfo(`❌ 兼容性检测失败: ${issues}`);

        // 在开发环境下输出详细报告
        if (process.env.NODE_ENV === 'development') {
          console.log(getCompatibilityReport());
        }
      }

      return compatibilityResult.isSupported;
    } catch (error) {
      console.error('兼容性检测异常:', error);
      setDebugInfo(`兼容性检测异常: ${error}`);
      return false;
    }
  }, []);

  // 清理HTML内容
  const cleanHTML = useCallback((html: string): string => {
    // 使用 DOMPurify 进行基础清理
    const cleaned = DOMPurify.sanitize(html, {
      ALLOWED_TAGS: ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'strong', 'em', 'a', 'ul', 'ol', 'li', 'br', 'code', 'pre'],
      ALLOWED_ATTR: ['href', 'target', 'rel'],
      KEEP_CONTENT: true
    });

    // 额外的清理规则
    return cleaned
      .replace(/(<a[^>]*?)>/gi, '$1 rel="noopener noreferrer" target="_blank">')
      .replace(/(<img[^>]*?)>/gi, '$1 loading="lazy">');
  }, []);

  // Phase 1: 直接渲染到DOM（仅初始化时调用）
  const renderMarkdown = useCallback(async (markdownContent: string) => {
    if (isProcessing || !editorRef.current) return;

    setIsProcessing(true);
    setDebugInfo('正在渲染Markdown...');

    try {
      const result = await processor.process(markdownContent);
      const html = String(result);
      const cleanedHTML = cleanHTML(html);

      // Phase 1: 直接设置DOM内容，不再使用React state
      editorRef.current.innerHTML = cleanedHTML;

      setDebugInfo(`渲染完成: ${markdownContent.length} 字符 → ${cleanedHTML.length} 字符HTML`);

    } catch (error) {
      console.error('Markdown渲染失败:', error);
      if (editorRef.current) {
        editorRef.current.innerHTML = `<p style="color: red;">渲染错误: ${error}</p>`;
      }
      setDebugInfo(`渲染失败: ${error}`);
    } finally {
      setIsProcessing(false);
    }
  }, [processor, cleanHTML, isProcessing]);

  // DOM到Markdown的简单转换（Phase 1 实现）
  const htmlToMarkdown = useCallback((element: HTMLElement): string => {
    // 简化的DOM到Markdown转换
    const lines: string[] = [];

    // 遍历所有块级元素
    const walker = document.createTreeWalker(
      element,
      NodeFilter.SHOW_ELEMENT,
      {
        acceptNode: (node) => {
          const el = node as HTMLElement;
          const tagName = el.tagName.toLowerCase();

          // 只处理块级元素
          if (['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'ul', 'ol', 'li', 'blockquote', 'pre'].includes(tagName)) {
            return NodeFilter.FILTER_ACCEPT;
          }
          return NodeFilter.FILTER_SKIP;
        }
      }
    );

    let node = walker.nextNode();
    while (node) {
      const el = node as HTMLElement;
      const tagName = el.tagName.toLowerCase();
      const text = el.textContent || '';

      if (text.trim()) {
        switch (tagName) {
          case 'h1':
            lines.push(`# ${text}`);
            break;
          case 'h2':
            lines.push(`## ${text}`);
            break;
          case 'h3':
            lines.push(`### ${text}`);
            break;
          case 'h4':
            lines.push(`#### ${text}`);
            break;
          case 'h5':
            lines.push(`##### ${text}`);
            break;
          case 'h6':
            lines.push(`###### ${text}`);
            break;
          case 'li':
            lines.push(`- ${text}`);
            break;
          case 'blockquote':
            lines.push(`> ${text}`);
            break;
          case 'pre':
            lines.push(`\`\`\`\n${text}\n\`\`\``);
            break;
          default:
            lines.push(text);
        }
      }

      node = walker.nextNode();
    }

    return lines.join('\n\n');
  }, []);

  // 被动同步DOM变化到外部状态
  const syncDOMToState = useCallback(() => {
    if (!editorRef.current || isProcessing) return;

    setDebugInfo('同步DOM到状态...');

    // 将DOM内容转换为Markdown
    const markdownContent = htmlToMarkdown(editorRef.current);

    // 通过onChange回调同步到外部（不会触发重新渲染，因为我们不再使用dangerouslySetInnerHTML）
    onChange(markdownContent);

    setDebugInfo(`同步完成: ${markdownContent.length} 字符`);
  }, [htmlToMarkdown, onChange, isProcessing]);

  // 节流函数
  const throttle = useCallback((func: Function, delay: number) => {
    let timeoutId: NodeJS.Timeout | null = null;
    let lastExecTime = 0;

    return (...args: any[]) => {
      const currentTime = Date.now();

      if (currentTime - lastExecTime > delay) {
        func(...args);
        lastExecTime = currentTime;
      } else {
        if (timeoutId) clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          func(...args);
          lastExecTime = Date.now();
        }, delay - (currentTime - lastExecTime));
      }
    };
  }, []);

  // 设置Phase 1事件监听器 - 让浏览器处理输入，被动同步
  const setupPhase1EventListeners = useCallback(() => {
    const el = editorRef.current;
    if (!el) return;

    console.log('🎯 设置Phase 1事件监听器（被动同步模式）...');

    // 节流的同步函数
    const throttledSync = throttle(syncDOMToState, 80);

    // input事件 - 浏览器完成输入后同步状态
    const handleInput = () => {
      setDebugInfo('检测到输入，同步中...');
      throttledSync();
    };

    // 粘贴事件 - 允许浏览器处理，然后同步
    const handlePaste = (e: ClipboardEvent) => {
      // 不阻止默认行为，让浏览器处理粘贴
      setDebugInfo('检测到粘贴');

      // 延迟同步，确保浏览器完成粘贴操作
      setTimeout(() => {
        syncDOMToState();
      }, 10);
    };

    // 键盘事件 - 处理一些特殊按键
    const handleKeyDown = (e: KeyboardEvent) => {
      // 可以在这里处理特殊快捷键，但Phase 1暂时不需要
      setDebugInfo(`按键: ${e.key}`);
    };

    // 绑定事件
    el.addEventListener('input', handleInput);
    el.addEventListener('paste', handlePaste);
    el.addEventListener('keydown', handleKeyDown);

    // 返回清理函数
    return () => {
      el.removeEventListener('input', handleInput);
      el.removeEventListener('paste', handlePaste);
      el.removeEventListener('keydown', handleKeyDown);
    };
  }, [syncDOMToState, throttle]);

  // 初始化兼容性检测
  useEffect(() => {
    const supported = checkWYSIWYGSupport();
    setIsSupported(supported);
    
    if (!supported && onModeSwitch) {
      // 不支持时自动切换到分屏模式
      setTimeout(() => {
        onModeSwitch('split');
      }, 1000);
    }
  }, [checkWYSIWYGSupport, onModeSwitch]);

  // Phase 1: 初始渲染（仅渲染一次）
  useEffect(() => {
    if (isSupported && content && editorRef.current && !isInitialized) {
      renderMarkdown(content).then(() => {
        setIsInitialized(true);
        setDebugInfo('初始渲染完成，进入被动同步模式');
      });
    }
  }, [content, renderMarkdown, isSupported, isInitialized]);

  // Phase 1: 设置被动同步事件监听器
  useEffect(() => {
    if (isSupported && isInitialized && editorRef.current) {
      const cleanup = setupPhase1EventListeners();
      setDebugInfo('Phase 1 事件监听器已设置');
      return cleanup;
    }
  }, [isSupported, isInitialized, setupPhase1EventListeners]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      // Phase 1 暂时不需要同步管理器
      setDebugInfo('组件卸载');
    };
  }, []);

  // 如果不支持，显示降级提示
  if (!isSupported) {
    return (
      <div className="flex items-center justify-center h-full bg-muted/50 rounded-lg border-2 border-dashed border-muted-foreground/25">
        <div className="text-center p-8">
          <div className="text-lg font-medium text-muted-foreground mb-2">
            WYSIWYG 模式不支持
          </div>
          <div className="text-sm text-muted-foreground mb-4">
            您的浏览器不支持所需的功能，正在切换到分屏模式...
          </div>
          <div className="text-xs text-muted-foreground">
            {debugInfo}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`wysiwyg-editor h-full flex flex-col ${className}`}>
      {/* 调试信息 - 开发阶段显示 */}
      {process.env.NODE_ENV === 'development' && (
        <div className="bg-blue-50 dark:bg-blue-950 p-2 text-xs text-blue-700 dark:text-blue-300 border-b">
          <strong>WYSIWYG调试:</strong> {debugInfo}
        </div>
      )}
      
      {/* 编辑器内容 - Phase 1: 只在初始化时设置HTML */}
      <div
        ref={editorRef}
        contentEditable={true}
        suppressContentEditableWarning={true}
        className="flex-1 prose prose-slate dark:prose-invert max-w-none p-4 overflow-auto focus:outline-none"
        style={{
          lineHeight: '1.7',
          fontSize: '16px',
          minHeight: '200px'
        }}
        data-placeholder={placeholder}
        // Phase 1: 不再使用dangerouslySetInnerHTML进行重复渲染
        // 内容由renderMarkdown在useEffect中直接设置到DOM
      />
      
      {/* 处理状态指示 */}
      {isProcessing && (
        <div className="absolute top-2 right-2 bg-blue-500 text-white px-2 py-1 rounded text-xs">
          处理中...
        </div>
      )}
    </div>
  );
};

export default WYSIWYGEditor;
