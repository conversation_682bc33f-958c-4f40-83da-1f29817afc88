'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkGfm from 'remark-gfm';
import remarkRehype from 'remark-rehype';
import rehypeHighlight from 'rehype-highlight';
import rehypeStringify from 'rehype-stringify';
import rehypeRaw from 'rehype-raw';
import DOMPurify from 'dompurify';
import { checkWYSIWYGCompatibility, getCompatibilityReport } from '@/lib/wysiwyg-support';
import { WYSIWYGSyncManager } from '@/lib/wysiwyg-sync';

// 光标位置保存和恢复工具函数
function saveSelection(): Range | null {
  const sel = window.getSelection();
  return sel && sel.rangeCount ? sel.getRangeAt(0).cloneRange() : null;
}

function restoreSelection(range: Range | null): void {
  if (!range) return;
  const sel = window.getSelection();
  if (sel) {
    sel.removeAllRanges();
    sel.addRange(range);
  }
}

interface WYSIWYGEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  className?: string;
  onModeSwitch?: (mode: string) => void;
}

export const WYSIWYGEditor: React.FC<WYSIWYGEditorProps> = ({
  content,
  onChange,
  placeholder = '开始编写您的 Markdown...',
  className = '',
  onModeSwitch
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const [renderedHTML, setRenderedHTML] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [isSupported, setIsSupported] = useState(true);
  const [debugInfo, setDebugInfo] = useState<string>('');
  const [isInitialized, setIsInitialized] = useState(false);
  const syncManagerRef = useRef<WYSIWYGSyncManager | null>(null);
  const isUserInputRef = useRef(false);
  const imeActiveRef = useRef(false); // 中文输入法状态

  // Markdown 处理器
  const processor = unified()
    .use(remarkParse)
    .use(remarkGfm)
    .use(remarkRehype, { allowDangerousHtml: true })
    .use(rehypeHighlight)
    .use(rehypeRaw)
    .use(rehypeStringify);

  // 浏览器兼容性检测
  const checkWYSIWYGSupport = useCallback((): boolean => {
    try {
      const compatibilityResult = checkWYSIWYGCompatibility();

      // 设置详细的调试信息
      if (compatibilityResult.isSupported) {
        setDebugInfo(`✅ 兼容性检测通过 - ${compatibilityResult.browserInfo.isChrome ? 'Chrome' :
                      compatibilityResult.browserInfo.isFirefox ? 'Firefox' :
                      compatibilityResult.browserInfo.isSafari ? 'Safari' :
                      compatibilityResult.browserInfo.isEdge ? 'Edge' : '未知浏览器'}`);
      } else {
        const issues = compatibilityResult.issues.join(', ');
        setDebugInfo(`❌ 兼容性检测失败: ${issues}`);

        // 在开发环境下输出详细报告
        if (process.env.NODE_ENV === 'development') {
          console.log(getCompatibilityReport());
        }
      }

      return compatibilityResult.isSupported;
    } catch (error) {
      console.error('兼容性检测异常:', error);
      setDebugInfo(`兼容性检测异常: ${error}`);
      return false;
    }
  }, []);

  // 清理HTML内容
  const cleanHTML = useCallback((html: string): string => {
    // 使用 DOMPurify 进行基础清理
    const cleaned = DOMPurify.sanitize(html, {
      ALLOWED_TAGS: ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'strong', 'em', 'a', 'ul', 'ol', 'li', 'br', 'code', 'pre'],
      ALLOWED_ATTR: ['href', 'target', 'rel'],
      KEEP_CONTENT: true
    });

    // 额外的清理规则
    return cleaned
      .replace(/(<a[^>]*?)>/gi, '$1 rel="noopener noreferrer" target="_blank">')
      .replace(/(<img[^>]*?)>/gi, '$1 loading="lazy">');
  }, []);

  // 渲染 Markdown 内容
  const renderMarkdown = useCallback(async (markdownContent: string) => {
    if (isProcessing) return;
    
    setIsProcessing(true);
    setDebugInfo('正在渲染Markdown...');

    try {
      const result = await processor.process(markdownContent);
      const html = String(result);
      const cleanedHTML = cleanHTML(html);
      
      setRenderedHTML(cleanedHTML);
      setDebugInfo(`渲染完成: ${markdownContent.length} 字符 → ${cleanedHTML.length} 字符HTML`);
      
    } catch (error) {
      console.error('Markdown渲染失败:', error);
      setRenderedHTML(`<p style="color: red;">渲染错误: ${error}</p>`);
      setDebugInfo(`渲染失败: ${error}`);
    } finally {
      setIsProcessing(false);
    }
  }, [processor, cleanHTML, isProcessing]);

  // 应用用户编辑操作
  const applyUserEdit = useCallback((event?: InputEvent | CompositionEvent) => {
    if (!editorRef.current || isProcessing) return;

    setDebugInfo(`用户编辑: ${event?.type || 'manual'}`);

    // 获取当前DOM内容
    const currentContent = editorRef.current.textContent || '';

    // 通过onChange回调同步到外部
    onChange(currentContent);

    // 如果有同步管理器，也通知它
    if (syncManagerRef.current) {
      syncManagerRef.current.updateMarkdownContent(currentContent, editorRef.current);
    }
  }, [onChange, isProcessing]);

  // 设置原生事件监听器
  const setupNativeEventListeners = useCallback(() => {
    const el = editorRef.current;
    if (!el) return;

    console.log('🎯 设置原生事件监听器...');

    // 中文输入法开始
    const handleCompositionStart = () => {
      imeActiveRef.current = true;
      setDebugInfo('中文输入开始');
    };

    // 输入前事件 - 核心事件处理
    const handleBeforeInput = (e: InputEvent) => {
      // 如果是中文输入法的临时字符，让IME处理
      if (imeActiveRef.current && e.inputType === 'insertCompositionText') {
        return;
      }

      // 保存光标位置
      const selection = saveSelection();

      // 应用用户编辑
      applyUserEdit(e);

      // 阻止默认DOM修改
      e.preventDefault();

      setDebugInfo(`beforeinput: ${e.inputType || 'unknown'}`);
    };

    // 中文输入法结束
    const handleCompositionEnd = (e: CompositionEvent) => {
      imeActiveRef.current = false;

      // 保存光标位置
      const selection = saveSelection();

      // 处理最终的中文字符
      applyUserEdit(e);

      // 恢复光标位置
      requestAnimationFrame(() => {
        restoreSelection(selection);
      });

      setDebugInfo(`中文输入完成: ${e.data}`);
    };

    // input事件作为兜底（理论上不应该触发，因为我们阻止了默认行为）
    const handleInput = () => {
      if (!imeActiveRef.current) {
        console.warn('意外的input事件触发');
      }
    };

    // 粘贴事件
    const handlePaste = (e: ClipboardEvent) => {
      e.preventDefault();

      const pastedText = e.clipboardData?.getData('text/plain') || '';
      setDebugInfo(`粘贴: ${pastedText.length} 字符`);

      // 使用execCommand插入纯文本
      document.execCommand('insertText', false, pastedText);
    };

    // 绑定事件
    el.addEventListener('compositionstart', handleCompositionStart);
    el.addEventListener('beforeinput', handleBeforeInput);
    el.addEventListener('compositionend', handleCompositionEnd);
    el.addEventListener('input', handleInput);
    el.addEventListener('paste', handlePaste);

    // 返回清理函数
    return () => {
      el.removeEventListener('compositionstart', handleCompositionStart);
      el.removeEventListener('beforeinput', handleBeforeInput);
      el.removeEventListener('compositionend', handleCompositionEnd);
      el.removeEventListener('input', handleInput);
      el.removeEventListener('paste', handlePaste);
    };
  }, [applyUserEdit]);

  // 初始化兼容性检测
  useEffect(() => {
    const supported = checkWYSIWYGSupport();
    setIsSupported(supported);
    
    if (!supported && onModeSwitch) {
      // 不支持时自动切换到分屏模式
      setTimeout(() => {
        onModeSwitch('split');
      }, 1000);
    }
  }, [checkWYSIWYGSupport, onModeSwitch]);

  // 初始渲染
  useEffect(() => {
    if (isSupported && content && editorRef.current) {
      renderMarkdown(content).then(() => {
        // 初始化同步管理器
        if (!syncManagerRef.current) {
          syncManagerRef.current = new WYSIWYGSyncManager();
          syncManagerRef.current.initialize(editorRef.current!, content);
          setDebugInfo('同步管理器已初始化');
        } else {
          syncManagerRef.current.updateMarkdownContent(content, editorRef.current!);
          setDebugInfo('同步管理器内容已更新');
        }

        setIsInitialized(true);
      });
    }
  }, [content, renderMarkdown, isSupported]);

  // 设置事件监听器
  useEffect(() => {
    if (isSupported && isInitialized && editorRef.current) {
      const cleanup = setupNativeEventListeners();
      setDebugInfo('原生事件监听器已设置');
      return cleanup;
    }
  }, [isSupported, isInitialized, setupNativeEventListeners]);

  // 组件卸载时清理同步管理器
  useEffect(() => {
    return () => {
      if (syncManagerRef.current) {
        syncManagerRef.current.destroy();
        syncManagerRef.current = null;
      }
    };
  }, []);

  // 如果不支持，显示降级提示
  if (!isSupported) {
    return (
      <div className="flex items-center justify-center h-full bg-muted/50 rounded-lg border-2 border-dashed border-muted-foreground/25">
        <div className="text-center p-8">
          <div className="text-lg font-medium text-muted-foreground mb-2">
            WYSIWYG 模式不支持
          </div>
          <div className="text-sm text-muted-foreground mb-4">
            您的浏览器不支持所需的功能，正在切换到分屏模式...
          </div>
          <div className="text-xs text-muted-foreground">
            {debugInfo}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`wysiwyg-editor h-full flex flex-col ${className}`}>
      {/* 调试信息 - 开发阶段显示 */}
      {process.env.NODE_ENV === 'development' && (
        <div className="bg-blue-50 dark:bg-blue-950 p-2 text-xs text-blue-700 dark:text-blue-300 border-b">
          <strong>WYSIWYG调试:</strong> {debugInfo}
        </div>
      )}
      
      {/* 编辑器内容 */}
      <div
        ref={editorRef}
        contentEditable={true}
        suppressContentEditableWarning={true}
        className="flex-1 prose prose-slate dark:prose-invert max-w-none p-4 overflow-auto focus:outline-none"
        style={{
          lineHeight: '1.7',
          fontSize: '16px',
          minHeight: '200px'
        }}
        dangerouslySetInnerHTML={{ __html: renderedHTML }}
        data-placeholder={placeholder}
      />
      
      {/* 处理状态指示 */}
      {isProcessing && (
        <div className="absolute top-2 right-2 bg-blue-500 text-white px-2 py-1 rounded text-xs">
          处理中...
        </div>
      )}
    </div>
  );
};

export default WYSIWYGEditor;
